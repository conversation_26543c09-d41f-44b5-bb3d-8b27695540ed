#!/bin/bash

# ===================================================================
# 咖啡数据文件测试脚本
# 用于检查CoffeeChain.csv文件的格式和内容
# ===================================================================

echo "=========================================="
echo "咖啡数据文件测试脚本"
echo "=========================================="

WORK_DIR="/home/<USER>/spark"
cd $WORK_DIR

echo "当前工作目录: $(pwd)"

# 检查文件是否存在
if [ -f "CoffeeChain.csv" ]; then
    echo "✓ CoffeeChain.csv 文件存在"
    
    # 显示文件信息
    echo ""
    echo "=== 文件信息 ==="
    echo "文件大小: $(du -h CoffeeChain.csv | cut -f1)"
    echo "文件行数: $(wc -l < CoffeeChain.csv)"
    
    # 显示文件头部
    echo ""
    echo "=== 文件头部 (前10行) ==="
    head -10 CoffeeChain.csv
    
    # 显示文件尾部
    echo ""
    echo "=== 文件尾部 (后5行) ==="
    tail -5 CoffeeChain.csv
    
    # 检查字段数量
    echo ""
    echo "=== 字段分析 ==="
    echo "表头字段数: $(head -1 CoffeeChain.csv | tr ',' '\n' | wc -l)"
    echo "表头内容:"
    head -1 CoffeeChain.csv | tr ',' '\n' | nl
    
    # 检查数据行的字段数量
    echo ""
    echo "第2行字段数: $(sed -n '2p' CoffeeChain.csv | tr ',' '\n' | wc -l)"
    echo "第3行字段数: $(sed -n '3p' CoffeeChain.csv | tr ',' '\n' | wc -l)"
    
    # 检查是否有空行
    echo ""
    echo "=== 数据质量检查 ==="
    empty_lines=$(grep -c '^$' CoffeeChain.csv)
    echo "空行数量: $empty_lines"
    
    # 检查字符编码
    echo "文件编码: $(file -b --mime-encoding CoffeeChain.csv)"
    
else
    echo "✗ CoffeeChain.csv 文件不存在"
    echo "请确保数据文件在正确位置: $WORK_DIR/CoffeeChain.csv"
    exit 1
fi

echo ""
echo "=========================================="
echo "数据文件检查完成"
echo "=========================================="
