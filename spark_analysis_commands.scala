// ===================================================================
// Spark Shell 综合分析命令脚本
// 适用于大数据实时处理技术期末考试
// 虚拟机路径: /home/<USER>/spark/
// ===================================================================

println("开始执行Spark数据分析任务...")
println("虚拟机路径: /home/<USER>/spark/")

// ===================================================================
// 第一部分：人口年龄数据生成和分析
// ===================================================================

println("\n=== 第一部分：人口年龄数据分析 ===")

// 1. 生成人口年龄数据
import scala.util.Random
import java.io.{File, PrintWriter}

val populationDataPath = "/home/<USER>/spark/population_data.txt"
val recordCount = 50000  // 生成5万条记录
val minAge = 18
val maxAge = 85

println(s"正在生成 $recordCount 条人口数据...")

val random = new Random()
val writer = new PrintWriter(new File(populationDataPath))

try {
  for (i <- 1 to recordCount) {
    val age = minAge + random.nextInt(maxAge - minAge + 1)
    writer.println(s"$i\t$age")
    if (i % 10000 == 0) {
      println(s"已生成 $i 条记录...")
    }
  }
} finally {
  writer.close()
}

println(s"人口数据生成完成！文件保存在: $populationDataPath")

// 2. 使用Spark分析人口年龄数据
val populationRDD = sc.textFile(populationDataPath)

println(s"读取到 ${populationRDD.count()} 条人口记录")

// 解析年龄数据
val ageRDD = populationRDD.map { line =>
  val parts = line.split("\t")
  parts(1).toInt
}

// 缓存数据
ageRDD.cache()

// 计算统计信息
val totalCount = ageRDD.count()
val totalAge = ageRDD.reduce(_ + _)
val averageAge = totalAge.toDouble / totalCount
val minAgeValue = ageRDD.min()
val maxAgeValue = ageRDD.max()

println(s"\n=== 人口年龄统计结果 ===")
println(s"总人口数量: $totalCount")
println(s"平均年龄: ${f"$averageAge%.2f"} 岁")
println(s"最小年龄: $minAgeValue 岁")
println(s"最大年龄: $maxAgeValue 岁")

// 年龄分布统计
val ageDistribution = ageRDD
  .map(age => (age / 10 * 10, 1))
  .reduceByKey(_ + _)
  .sortByKey()
  .collect()

println(s"\n=== 年龄分布情况 ===")
println("年龄段\t人数\t占比")
ageDistribution.foreach { case (ageGroup, count) =>
  val percentage = count.toDouble / totalCount * 100
  println(s"${ageGroup}-${ageGroup + 9}岁\t$count\t${f"$percentage%.2f"}%")
}

// 保存人口分析结果
val populationResults = Array(
  "=== 人口年龄统计分析报告 ===",
  s"分析时间: ${new java.util.Date()}",
  s"总人口数量: $totalCount",
  s"平均年龄: ${f"$averageAge%.2f"} 岁",
  s"最小年龄: $minAgeValue 岁",
  s"最大年龄: $maxAgeValue 岁",
  "",
  "=== 年龄分布情况 ==="
) ++ ageDistribution.map { case (ageGroup, count) =>
  val percentage = count.toDouble / totalCount * 100
  s"${ageGroup}-${ageGroup + 9}岁: $count 人 (${f"$percentage%.2f"}%)"
}

val populationResultRDD = sc.parallelize(populationResults)
populationResultRDD.coalesce(1).saveAsTextFile("/home/<USER>/spark/population_analysis_result")

println("人口年龄分析结果已保存到: /home/<USER>/spark/population_analysis_result")

// ===================================================================
// 第二部分：咖啡连锁店数据分析
// ===================================================================

println("\n=== 第二部分：咖啡连锁店数据分析 ===")

// 定义咖啡记录数据结构
case class CoffeeRecord(
  area_code: String, area: String, sales: Double, cogs: Double,
  total_expenses: Double, marketing: Double, inventory: Double,
  budget_profit: Double, budget_cogs: Double, budget_margin: Double,
  date: String, product_line: String, product_type: String,
  product: String, type_name: String, market_size: String,
  market: String, state: String
)

// 解析CSV数据的函数
def parseCSVLine(line: String): CoffeeRecord = {
  val parts = line.split(",").map(_.trim.replaceAll("\"", ""))
  
  CoffeeRecord(
    area_code = if (parts.length > 0) parts(0) else "",
    area = if (parts.length > 1) parts(1) else "",
    sales = if (parts.length > 2 && parts(2).nonEmpty) parts(2).toDouble else 0.0,
    cogs = if (parts.length > 3 && parts(3).nonEmpty) parts(3).toDouble else 0.0,
    total_expenses = if (parts.length > 4 && parts(4).nonEmpty) parts(4).toDouble else 0.0,
    marketing = if (parts.length > 5 && parts(5).nonEmpty) parts(5).toDouble else 0.0,
    inventory = if (parts.length > 6 && parts(6).nonEmpty) parts(6).toDouble else 0.0,
    budget_profit = if (parts.length > 7 && parts(7).nonEmpty) parts(7).toDouble else 0.0,
    budget_cogs = if (parts.length > 8 && parts(8).nonEmpty) parts(8).toDouble else 0.0,
    budget_margin = if (parts.length > 9 && parts(9).nonEmpty) parts(9).toDouble else 0.0,
    date = if (parts.length > 10) parts(10) else "",
    product_line = if (parts.length > 11) parts(11) else "",
    product_type = if (parts.length > 12) parts(12) else "",
    product = if (parts.length > 13) parts(13) else "",
    type_name = if (parts.length > 14) parts(14) else "",
    market_size = if (parts.length > 15) parts(15) else "",
    market = if (parts.length > 16) parts(16) else "",
    state = if (parts.length > 17) parts(17) else ""
  )
}

// 读取咖啡数据
val coffeeDataPath = "/home/<USER>/spark/CoffeeChain.csv"
val rawCoffeeData = sc.textFile(coffeeDataPath)

// 获取表头并过滤
val header = rawCoffeeData.first()
val coffeeDataRDD = rawCoffeeData.filter(_ != header).map(parseCSVLine)

// 缓存数据
coffeeDataRDD.cache()

val totalCoffeeRecords = coffeeDataRDD.count()
println(s"读取到 $totalCoffeeRecords 条咖啡销售记录")

// 1. 咖啡产品销售排名
println("\n=== 咖啡产品销售排名分析 ===")
val productSales = coffeeDataRDD
  .map(record => (record.product, record.sales))
  .reduceByKey(_ + _)
  .sortBy(_._2, false)
  .take(15)

println("排名\t产品名称\t总销售额")
productSales.zipWithIndex.foreach { case ((product, sales), index) =>
  println(s"${index + 1}\t$product\t${f"$sales%.2f"}")
}

// 2. 各州销售量分析
println("\n=== 各州销售量分析 ===")
val stateSales = coffeeDataRDD
  .map(record => (record.state, record.sales))
  .reduceByKey(_ + _)
  .sortBy(_._2, false)
  .collect()

println("州名\t总销售额")
stateSales.foreach { case (state, sales) =>
  println(s"$state\t${f"$sales%.2f"}")
}

// 3. 市场规模分析
println("\n=== 市场规模分析 ===")
val marketSales = coffeeDataRDD
  .map(record => (record.market, record.sales))
  .reduceByKey(_ + _)
  .sortBy(_._2, false)
  .collect()

println("市场\t总销售额")
marketSales.foreach { case (market, sales) =>
  println(s"$market\t${f"$sales%.2f"}")
}

// 4. 利润分析
println("\n=== 利润和成本分析 ===")
val totalSales = coffeeDataRDD.map(_.sales).sum()
val totalProfit = coffeeDataRDD.map(_.budget_profit).sum()
val totalCogs = coffeeDataRDD.map(_.cogs).sum()
val avgSales = coffeeDataRDD.map(_.sales).mean()
val avgProfit = coffeeDataRDD.map(_.budget_profit).mean()
val avgCogs = coffeeDataRDD.map(_.cogs).mean()

println(s"总销售额: ${f"$totalSales%.2f"}")
println(s"总利润: ${f"$totalProfit%.2f"}")
println(s"总成本: ${f"$totalCogs%.2f"}")
println(s"平均销售额: ${f"$avgSales%.2f"}")
println(s"平均利润: ${f"$avgProfit%.2f"}")
println(s"平均成本: ${f"$avgCogs%.2f"}")
println(s"利润率: ${f"${(totalProfit / totalSales) * 100}%.2f"}%")

// 保存咖啡分析结果
val coffeeResults = Array(
  "=== 咖啡连锁店数据分析报告 ===",
  s"分析时间: ${new java.util.Date()}",
  s"总记录数: $totalCoffeeRecords",
  s"总销售额: ${f"$totalSales%.2f"}",
  s"总利润: ${f"$totalProfit%.2f"}",
  s"利润率: ${f"${(totalProfit / totalSales) * 100}%.2f"}%",
  "",
  "=== 销售排名前10的产品 ==="
) ++ productSales.take(10).zipWithIndex.map { case ((product, sales), index) =>
  s"${index + 1}. $product: ${f"$sales%.2f"}"
}

val coffeeResultRDD = sc.parallelize(coffeeResults)
coffeeResultRDD.coalesce(1).saveAsTextFile("/home/<USER>/spark/coffee_analysis_result")

println("咖啡分析结果已保存到: /home/<USER>/spark/coffee_analysis_result")

// 清理缓存
ageRDD.unpersist()
coffeeDataRDD.unpersist()

println("\n=== 所有分析任务完成！ ===")
println("结果文件位置:")
println("- 人口分析结果: /home/<USER>/spark/population_analysis_result")
println("- 咖啡分析结果: /home/<USER>/spark/coffee_analysis_result")
