# 咖啡数据分析故障排除指南

## 问题描述
人口分析结果成功，但是咖啡分析结果生成目录不存在。

## 可能原因分析

### 1. CSV数据格式问题
- 数据文件编码不正确
- CSV字段分隔符问题
- 数据中包含特殊字符
- 字段数量不匹配

### 2. 程序编译或运行错误
- Scala编译失败
- Spark程序运行时异常
- 内存不足导致程序崩溃
- 数据解析异常

### 3. 文件路径问题
- 输入文件路径不正确
- 输出目录权限问题
- 磁盘空间不足

## 解决方案

### 步骤1: 检查数据文件
```bash
cd /home/<USER>/spark/

# 给测试脚本执行权限
chmod +x test_coffee_data.sh

# 运行数据文件测试
./test_coffee_data.sh
```

### 步骤2: 使用简化版分析器
如果完整版分析器失败，使用简化版：

```bash
# 编译简化版分析器
scalac -cp "$SPARK_HOME/jars/*" SimpleCoffeeAnalyzer.scala

# 运行简化版分析器
spark-submit --class SimpleCoffeeAnalyzer --master local[*] --driver-memory 2g --executor-memory 2g .
```

### 步骤3: 手动检查错误
```bash
# 查看Spark日志
ls -la /tmp/spark-*

# 检查系统资源
free -h
df -h

# 检查Java进程
jps
```

### 步骤4: 重新运行自动化脚本
```bash
# 重新运行完整的分析脚本
./execute_analysis.sh
```

## 预期结果

### 成功情况1: 完整版分析器成功
会生成以下目录结构：
```
/home/<USER>/spark/coffee_analysis/
├── preprocessing/
├── sales_ranking/
├── distribution_analysis/
├── state_sales_analysis/
├── market_sales_analysis/
└── profit_price_analysis/
```

### 成功情况2: 简化版分析器成功
会生成以下目录：
```
/home/<USER>/spark/coffee_simple_analysis/
└── part-00000
```

## 查看结果

### 完整版结果
```bash
# 查看销售排名
cat /home/<USER>/spark/coffee_analysis/sales_ranking/part-00000

# 查看数据预处理结果
cat /home/<USER>/spark/coffee_analysis/preprocessing/part-00000

# 查看销售分布分析
cat /home/<USER>/spark/coffee_analysis/distribution_analysis/part-00000
```

### 简化版结果
```bash
# 查看简化版分析结果
cat /home/<USER>/spark/coffee_simple_analysis/part-00000
```

## 常见错误信息及解决方法

### 错误1: "java.lang.NumberFormatException"
**原因**: CSV数据中包含无法解析的数字格式
**解决**: 使用SimpleCoffeeAnalyzer，它有更好的错误处理

### 错误2: "java.io.FileNotFoundException"
**原因**: CoffeeChain.csv文件不存在或路径错误
**解决**: 
```bash
# 检查文件是否存在
ls -la /home/<USER>/spark/CoffeeChain.csv

# 如果不存在，确保文件在正确位置
```

### 错误3: "java.lang.OutOfMemoryError"
**原因**: 内存不足
**解决**: 
```bash
# 增加内存配置
spark-submit --driver-memory 4g --executor-memory 4g --class ClassName .
```

### 错误4: "org.apache.spark.SparkException"
**原因**: Spark运行时错误
**解决**: 
```bash
# 检查Spark配置
echo $SPARK_HOME
spark-submit --version

# 重启Spark
```

## 验证步骤

1. **检查文件生成**:
```bash
ls -la /home/<USER>/spark/
```

2. **验证结果内容**:
```bash
# 如果有coffee_analysis目录
find /home/<USER>/spark/coffee_analysis -name "part-00000" -exec cat {} \;

# 如果有coffee_simple_analysis目录
cat /home/<USER>/spark/coffee_simple_analysis/part-00000
```

3. **确认分析完整性**:
- 人口分析结果: ✓ 成功
- 咖啡分析结果: 需要验证

## 备用方案

如果所有方法都失败，可以使用Spark Shell手动执行：

```bash
# 启动Spark Shell
spark-shell --driver-memory 2g --executor-memory 2g

# 在Spark Shell中执行
scala> val data = sc.textFile("/home/<USER>/spark/CoffeeChain.csv")
scala> val header = data.first()
scala> println(s"Header: $header")
scala> println(s"Total lines: ${data.count()}")
scala> data.take(5).foreach(println)
```

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 错误日志输出
2. 数据文件检查结果
3. 系统环境信息
4. Spark版本信息

---

**注意**: 简化版分析器专门设计用于处理数据格式问题，即使完整版失败，简化版通常也能成功运行并生成基本的分析结果。
