import org.apache.spark.{SparkConf, SparkContext}

/**
 * 简化版咖啡连锁店数据分析器
 * 专门处理CSV数据解析问题，确保程序能够成功运行
 */
object SimpleCoffeeAnalyzer {
  
  def main(args: Array[String]): Unit = {
    // 创建Spark配置
    val conf = new SparkConf()
      .setAppName("SimpleCoffeeAnalyzer")
      .setMaster("local[*]")
      .set("spark.sql.adaptive.enabled", "true")
      .set("spark.sql.adaptive.coalescePartitions.enabled", "true")
    
    // 创建SparkContext
    val sc = new SparkContext(conf)
    
    try {
      // 输入和输出路径
      val inputPath = "/home/<USER>/spark/CoffeeChain.csv"
      val outputPath = "/home/<USER>/spark/coffee_simple_analysis"
      
      println("开始简化版咖啡连锁店数据分析...")
      println(s"输入文件: $inputPath")
      println(s"输出目录: $outputPath")
      
      // 执行分析
      analyzeCoffeeDataSimple(sc, inputPath, outputPath)
      
      println("咖啡连锁店数据分析完成！")
      
    } catch {
      case e: Exception =>
        println(s"分析过程中出现错误: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      sc.stop()
    }
  }
  
  /**
   * 简化版咖啡数据分析
   */
  def analyzeCoffeeDataSimple(sc: SparkContext, inputPath: String, outputPath: String): Unit = {
    // 读取CSV文件
    val rawData = sc.textFile(inputPath)
    
    println(s"读取到 ${rawData.count()} 行数据")
    
    // 获取表头
    val header = rawData.first()
    println(s"数据表头: $header")
    
    // 过滤掉表头
    val dataLines = rawData.filter(_ != header)
    
    println(s"数据行数: ${dataLines.count()}")
    
    // 显示前几行数据用于调试
    println("前5行数据:")
    dataLines.take(5).foreach(println)
    
    // 简单的数据分析 - 不依赖复杂的数据结构
    val results = Array(
      "=== 咖啡连锁店数据分析报告 ===",
      s"分析时间: ${new java.util.Date()}",
      s"数据文件: $inputPath",
      "",
      "=== 基本信息 ===",
      s"数据表头: $header",
      s"总行数: ${rawData.count()}",
      s"数据行数: ${dataLines.count()}",
      "",
      "=== 数据样例 ===",
      "前5行数据:"
    ) ++ dataLines.take(5).zipWithIndex.map { case (line, index) =>
      s"第${index + 1}行: $line"
    }
    
    // 尝试进行简单的数值分析
    try {
      // 假设第3列是销售数据（索引为2）
      val salesData = dataLines.map { line =>
        val parts = line.split(",")
        try {
          if (parts.length > 2) {
            val salesStr = parts(2).trim.replaceAll("\"", "")
            if (salesStr.nonEmpty && salesStr != "null") salesStr.toDouble else 0.0
          } else 0.0
        } catch {
          case _: NumberFormatException => 0.0
        }
      }.filter(_ > 0)
      
      if (salesData.count() > 0) {
        val totalSales = salesData.sum()
        val avgSales = salesData.mean()
        val maxSales = salesData.max()
        val minSales = salesData.min()
        
        val salesResults = Array(
          "",
          "=== 销售数据分析 ===",
          s"有效销售记录数: ${salesData.count()}",
          s"总销售额: ${f"$totalSales%.2f"}",
          s"平均销售额: ${f"$avgSales%.2f"}",
          s"最高销售额: ${f"$maxSales%.2f"}",
          s"最低销售额: ${f"$minSales%.2f"}"
        )
        
        val allResults = results ++ salesResults
        sc.parallelize(allResults).coalesce(1).saveAsTextFile(outputPath)
        
        println("\n=== 销售数据分析结果 ===")
        salesResults.foreach(println)
      } else {
        sc.parallelize(results).coalesce(1).saveAsTextFile(outputPath)
        println("无法解析销售数据，仅保存基本信息")
      }
      
    } catch {
      case e: Exception =>
        println(s"数值分析失败: ${e.getMessage}")
        sc.parallelize(results).coalesce(1).saveAsTextFile(outputPath)
    }
    
    println(s"分析结果已保存到: $outputPath")
  }
}
