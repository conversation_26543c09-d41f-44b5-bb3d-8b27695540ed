import org.apache.spark.{SparkConf, SparkContext}

/**
 * 人口年龄分析器
 * 使用Spark RDD计算人口平均年龄
 */
object PopulationAgeAnalyzer {
  
  def main(args: Array[String]): Unit = {
    // 创建Spark配置
    val conf = new SparkConf()
      .setAppName("PopulationAgeAnalyzer")
      .setMaster("local[*]")
      .set("spark.sql.adaptive.enabled", "true")
      .set("spark.sql.adaptive.coalescePartitions.enabled", "true")
    
    // 创建SparkContext
    val sc = new SparkContext(conf)
    
    try {
      // 输入和输出路径
      val inputPath = "/home/<USER>/spark/population_data.txt"
      val outputPath = "/home/<USER>/spark/population_analysis_result"
      
      println("开始分析人口年龄数据...")
      println(s"输入文件: $inputPath")
      println(s"输出目录: $outputPath")
      
      // 执行分析
      analyzePopulationAge(sc, inputPath, outputPath)
      
      println("人口年龄分析完成！")
      
    } catch {
      case e: Exception =>
        println(s"分析过程中出现错误: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      sc.stop()
    }
  }
  
  /**
   * 分析人口年龄数据
   */
  def analyzePopulationAge(sc: SparkContext, inputPath: String, outputPath: String): Unit = {
    // 读取数据文件
    val dataRDD = sc.textFile(inputPath)
    
    println(s"总记录数: ${dataRDD.count()}")
    
    // 解析数据，提取年龄
    val ageRDD = dataRDD.map { line =>
      val parts = line.split("\t")
      if (parts.length >= 2) {
        parts(1).toInt  // 年龄在第二列
      } else {
        throw new IllegalArgumentException(s"数据格式错误: $line")
      }
    }
    
    // 缓存RDD以提高性能
    ageRDD.cache()
    
    // 计算统计信息
    val totalCount = ageRDD.count()
    val totalAge = ageRDD.reduce(_ + _)
    val averageAge = totalAge.toDouble / totalCount
    
    val minAge = ageRDD.min()
    val maxAge = ageRDD.max()
    
    // 计算年龄分布
    val ageDistribution = ageRDD
      .map(age => (age / 10 * 10, 1))  // 按10岁分组
      .reduceByKey(_ + _)
      .sortByKey()
      .collect()
    
    // 准备结果数据
    val results = Array(
      "=== 人口年龄统计分析报告 ===",
      s"分析时间: ${new java.util.Date()}",
      s"数据文件: $inputPath",
      "",
      "=== 基本统计信息 ===",
      s"总人口数量: $totalCount",
      s"年龄总和: $totalAge",
      s"平均年龄: ${f"$averageAge%.2f"} 岁",
      s"最小年龄: $minAge 岁",
      s"最大年龄: $maxAge 岁",
      "",
      "=== 年龄分布情况 ===",
      "年龄段\t人数\t占比"
    ) ++ ageDistribution.map { case (ageGroup, count) =>
      val percentage = count.toDouble / totalCount * 100
      s"${ageGroup}-${ageGroup + 9}岁\t$count\t${f"$percentage%.2f"}%"
    }
    
    // 保存结果到文件
    val resultRDD = sc.parallelize(results)
    resultRDD.coalesce(1).saveAsTextFile(outputPath)
    
    // 在控制台显示结果
    println("\n=== 分析结果 ===")
    results.foreach(println)
    
    // 清理缓存
    ageRDD.unpersist()
  }
}
