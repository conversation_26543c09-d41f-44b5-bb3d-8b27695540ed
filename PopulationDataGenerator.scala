import scala.util.Random
import java.io.{File, PrintWriter}

/**
 * 人口数据生成器
 * 生成包含序号和年龄的人口数据文件
 */
object PopulationDataGenerator {
  
  def main(args: Array[String]): Unit = {
    // 配置参数
    val outputPath = "/home/<USER>/spark/population_data.txt"
    val recordCount = 100000  // 生成10万条记录
    val minAge = 18
    val maxAge = 90
    
    println("开始生成人口数据...")
    println(s"输出路径: $outputPath")
    println(s"记录数量: $recordCount")
    println(s"年龄范围: $minAge - $maxAge")
    
    generatePopulationData(outputPath, recordCount, minAge, maxAge)
    
    println("人口数据生成完成！")
    println(s"文件保存在: $outputPath")
    
    // 显示前10行数据作为示例
    showSampleData(outputPath, 10)
  }
  
  /**
   * 生成人口数据文件
   */
  def generatePopulationData(outputPath: String, count: Int, minAge: Int, maxAge: Int): Unit = {
    val random = new Random()
    val writer = new PrintWriter(new File(outputPath))
    
    try {
      for (i <- 1 to count) {
        val age = minAge + random.nextInt(maxAge - minAge + 1)
        writer.println(s"$i\t$age")
        
        // 每10000条记录显示进度
        if (i % 10000 == 0) {
          println(s"已生成 $i 条记录...")
        }
      }
    } finally {
      writer.close()
    }
  }
  
  /**
   * 显示文件的前几行数据
   */
  def showSampleData(filePath: String, lines: Int): Unit = {
    println(s"\n前 $lines 行数据示例:")
    println("序号\t年龄")
    println("----\t----")
    
    val source = scala.io.Source.fromFile(filePath)
    try {
      source.getLines().take(lines).foreach(println)
    } finally {
      source.close()
    }
  }
}
